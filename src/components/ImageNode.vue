<script setup>
import { ref, nextTick, computed, onMounted, onUnmounted, watch } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { Position, Handle, useVueFlow } from '@vue-flow/core'; // 确保此处导入了 getNodes
import { NodeToolbar } from '@vue-flow/node-toolbar';
import Icon from './Icon.vue';
import { backendApi } from '@/http/api';
import { findNextAvailablePosition } from '@/utils/node-position';
import { useFlowStore } from '@/store/modules/flowStore';

const flowStore = useFlowStore();

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
});

// Define emits to fix the warning
const emit = defineEmits(['updateNodeInternals']);

const uploadedImage = ref(null);
const fileInput = ref(null);
const aspectRatio = ref(null);

const { updateNodeData, onNodeClick, findNode, getEdges, getNodes, fitView } = useVueFlow();

const ALLOWED_RATIOS = [
  '1/1', // 1:1
  '2/3', // 2:3
  '3/4', // 3:4
  '16/9', // 16:9
  '21/9', // 21:9
  '3/2', // 3:2
  '4/3', // 4:3
  '9/16', // 9:16
  '9/21', // 9:21
];

const RATIO_LABELS = {
  '1/1': '1:1',
  '2/3': '2:3',
  '3/4': '3:4',
  '16/9': '16:9',
  '21/9': '21:9',
  '3/2': '3:2',
  '4/3': '4:3',
  '9/16': '9:16',
  '9/21': '9:21',
};

// Style options with thumbnails
const STYLE_OPTIONS = [
  { name: 'Original', label: 'Original', icon: 'mdi:image-outline' },
  { name: 'Vintage', label: 'Vintage', icon: 'mdi:image-filter-vintage' },
  { name: 'Noir', label: 'Noir', icon: 'mdi:image-filter-black-white' },
  { name: 'Sketch', label: 'Sketch', icon: 'mdi:drawing' },
  { name: 'Watercolor', label: 'Watercolor', icon: 'mdi:water' },
  { name: 'Fisheye', label: 'Fisheye', icon: 'mdi:circle-expand' },
  { name: 'Motion Blur', label: 'Motion Blur', icon: 'mdi:motion' },
  { name: 'Monochrome', label: 'Monochrome', icon: 'mdi:palette-outline' },
  { name: 'realistic', label: 'Realistic', icon: 'mdi:image-auto-adjust' },
  { name: 'cartoon', label: 'Cartoon', icon: 'mdi:palette-swatch' },
  { name: 'minimalist', label: 'Minimalist', icon: 'mdi:triangle-outline' },
  { name: '3d-render', label: '3D Render', icon: 'mdi:cube-scan' },
];

// Model options
const MODEL_OPTIONS = [
  { id: 'sora_image', label: 'Sora Image', icon: 'mdi:movie-open' },
  { id: 'gpt-4o-image-vip', label: 'GPT 4o Image VIP', icon: 'mdi:robot' },
  { id: 'gemini-2.0-flash-exp-image-generation', label: 'Gemini 2.0 Image', icon: 'mdi:google' },
  { id: 'kling-v1', label: 'Kling V1 Image', icon: 'mdi:alien' },
  { id: 'kling-v1.5', label: 'Kling V1.5 Image', icon: 'mdi:alien-outline' },
  { id: 'kling-v2', label: 'Kling V2 Image', icon: 'mdi:space-invaders' },
  { id: 'flux-kontext', label: 'Flux Kontext Image', icon: 'mdi:image-filter-hdr' },
  // { id: 'flux-kontext-pro', label: 'Flux Kontext Pro Image', icon: 'mdi:image-filter-hdr' },
  // { id: 'flux-kontext-max', label: 'Flux Kontext Max Image', icon: 'mdi:image-filter-hdr' },
];

// Connected image nodes
const connectedImageNodes = computed(() => {
  const edges = getEdges.value;
  const connectedImages = [];

  edges.forEach((edge) => {
    if (edge.target === props.id) {
      const sourceNode = findNode(edge.source);
      if (sourceNode?.type === 'image' && sourceNode.data?.imageUrl) {
        connectedImages.push({
          id: sourceNode.id,
          imageUrl: sourceNode.data.imageUrl,
          aspectRatio: sourceNode.data.aspectRatio || '1/1',
        });
      }
    }
  });

  return connectedImages;
});

// Initialize with existing image URL if available in node data
if (props.data?.imageUrl) {
  uploadedImage.value = props.data.imageUrl;
  const initialNodeRatio = props.data.aspectRatio;
  aspectRatio.value = initialNodeRatio || '1/1'; // Set local ref for node's visual aspect ratio

  // If the node's data didn't have an aspectRatio, update it to the default '1/1'
  // This ensures the UI (e.g., selected ratio button) and persisted state are consistent.
  if (!initialNodeRatio) {
    updateNodeData(props.id, { aspectRatio: '1/1' });
  }
} else {
  // For new or existing imageless nodes, enforce '1/1' aspect ratio.
  aspectRatio.value = '1/1'; // Set local ref for node's visual aspect ratio

  // Update the node's data to '1/1' if it's not already set to this value.
  // This ensures the "1:1" ratio button is selected and the state is persisted.
  if (props.data.aspectRatio !== '1/1') {
    updateNodeData(props.id, { aspectRatio: '1/1' });
  }
}

// Handle style initialization (persisted data)
// Ensure default style is 'Original' if not already set in props.data.
// This will make the 'Original' button appear selected in the UI.
if (!props.data.style) {
  // This covers undefined, null, or empty string cases.
  updateNodeData(props.id, { style: 'Original' });
}

// Handle model initialization (persisted data)
// Ensure a default model is set if not already present.
if (!props.data.model && MODEL_OPTIONS.length > 0) {
  updateNodeData(props.id, { model: MODEL_OPTIONS[0].id });
}

// Listen for node click events from Vue Flow
onNodeClick(({ node }) => {
  if (node.id === props.id) {
    isHovering.value = true;
  }
});

// Toolbar actions (reactive based on whether image is uploaded)
const toolbarActions = computed(() => {
  // Determine if the "Enhance Prompt" button should be disabled.
  // It's disabled if:
  // 1. The input field (inputValue) is empty or contains only whitespace.
  const enhanceDisabled = inputValue.value.trim() === '' || isEnhancing.value || isGenerating.value;

  // 基础操作按钮 - 不包括 enhance
  const baseActions = [
    { id: 'style', label: 'Style', icon: 'mdi:palette-outline' },
    { id: 'ratio', label: 'Ratio', icon: 'mdi:aspect-ratio' },
    { id: 'model', label: 'Model', icon: 'mdi:cube-outline' },
  ];

  if (uploadedImage.value) {
    // 如果有上传图片，添加背景移除和描述图片按钮，但不添加增强提示按钮
    return [
      ...baseActions,
      { id: 'bg-remove', label: 'Remove Background', icon: 'mdi:image-filter-black-white' },
      { id: 'describe', label: 'Describe Image', icon: 'mdi:comment-text-outline' },
    ];
  } else {
    // 如果没有上传图片，添加增强提示按钮
    return [
      ...baseActions,
      { id: 'enhance', label: 'Enhance Prompt', icon: 'mdi:auto-fix', disabled: enhanceDisabled },
    ];
  }
});

// 过滤当前比例的计算属性
const filteredRatios = computed(() => {
  // 未上传时显示全部选项
  if (!uploadedImage.value) return ALLOWED_RATIOS;

  // 已上传时过滤当前比例
  return aspectRatio.value ? ALLOWED_RATIOS.filter((r) => r !== aspectRatio.value) : ALLOWED_RATIOS;
});

const parseAspectRatio = (ratioStr) => {
  const [numerator, denominator] = ratioStr.split('/').map(Number);
  return numerator / denominator;
};

const findClosestRatio = (originalRatio) => {
  return ALLOWED_RATIOS.reduce((closest, ratioStr) => {
    const currentVal = parseAspectRatio(ratioStr);
    const closestVal = parseAspectRatio(closest);
    const currentDiff = Math.abs(originalRatio - currentVal);
    const closestDiff = Math.abs(originalRatio - closestVal);
    return currentDiff < closestDiff ? ratioStr : closest;
  }, ALLOWED_RATIOS[0]);
};

const handleFileUpload = async (e) => {
  const file = e.target.files[0];
  if (file && file.type.startsWith('image/')) {
    try {
      // Create form data for the file upload
      const formData = new FormData();
      formData.append('file', file);

      // Upload the file to the backend
      const response = await backendApi.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Get the URL from the response
      const imageUrl = response.data.url;

      // Load the image to get dimensions for aspect ratio
      const img = new Image();
      img.onload = () => {
        const originalRatio = img.width / img.height;
        aspectRatio.value = findClosestRatio(originalRatio);
        uploadedImage.value = imageUrl;

        // Update node data with default values
        updateNodeData(props.id, {
          style: 'Original',
          backgroundRemoved: false,
          aspectRatio: aspectRatio.value || '1/1', // 添加默认值
          imageUrl: imageUrl, // Store the URL in node data
        });

        // Emit updateNodeInternals after updating image/aspect ratio
        emit('updateNodeInternals', props.id);
      };

      // Set the image source to the URL from the backend
      img.src = imageUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    }
  }
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

// State management
const isHovering = ref(false);
const toolbarPosition = ref({ x: 0, y: 0 });
const isInputActive = ref(false);

// State for prompt enhancement
const isEnhancing = ref(false);
const enhanceError = ref(null); // 用于显示增强过程中的特定错误

// Toolbar submenu states
const showStyleMenu = ref(false);
const showRatioMenu = ref(false);
const showModelMenu = ref(false); // State for model submenu

const handleDescribeImage = () => {
  if (!uploadedImage.value) return;

  const currentNode = findNode(props.id);
  if (!currentNode) {
    console.warn('Source node not found for describing image.');
    return;
  }

  // 生成新节点的ID
  const newNodePosition = findNextAvailablePosition(currentNode, 'text', getNodes.value);

  const newNodeId = flowStore.addNodeWithDetails({
    type: 'text',
    position: newNodePosition,
    data: {
      input: 'Describe this image in short',
    },
  });

  if (newNodeId) {
    flowStore.addEdgeWithDetails({
      source: props.id,
      target: newNodeId,
      animated: true,
      label: 'describe',
      style: { stroke: '#646cff' },
    });
  }

  setTimeout(() => fitView({ nodes: [props.id, newNodeId], animated: true, minZoom: 0.5, maxZoom: 1 })); // Fit view to both nodes
};

const handleMouseLeave = () => {
  if (!props.data?.selected) {
    isHovering.value = false;
    // Close submenus when leaving the node
    showStyleMenu.value = false;
    showRatioMenu.value = false;
    showModelMenu.value = false; // Add this line
  }
};

const inputValue = ref(props.data?.input || ''); // Initialize with existing input data
// Add debounced input update
const debouncedUpdate = useDebounceFn((value) => {
  updateNodeData(props.id, { input: value });
}, 500);
watch(inputValue, (newVal) => {
  debouncedUpdate(newVal);
});
const inputRef = ref(null);
const isGenerating = ref(false); // State for image generation
const generationError = ref(null); // State for generation errors
const pollingIntervalId = ref(null); // 新增: 用于存储轮询的 interval ID

// Initialize inputValue and activate input if props.data.input exists on mount
onMounted(() => {
  if (props.data?.input) {
    inputValue.value = props.data.input;
    isInputActive.value = true; // Activate input area
  }
  // 检查节点数据中是否有未完成的任务ID，如果在则恢复轮询
  if (props.data?.taskId && props.data?.status === 'processing') {
    isGenerating.value = true; // 恢复生成状态
    pollTaskStatus(props.data.taskId); // 恢复轮询
  }
});

// Input activation
const activateInput = async () => {
  isInputActive.value = true;
  await nextTick();
  inputRef.value?.focus();
};

// Blur handling
const deactivateInputIfEmpty = () => {
  setTimeout(() => {
    if (!inputValue.value && document.activeElement !== inputRef.value) {
      isInputActive.value = false;
    }
  }, 100);
};

// Image generation function
const generateImage = async () => {
  if (!inputValue.value.trim() || isGenerating.value) return;

  // Ensure a model is selected, as it's a required API parameter.
  const currentModel = props.data.model || (MODEL_OPTIONS.length > 0 ? MODEL_OPTIONS[0].id : undefined);
  if (!currentModel) {
    console.error('Error: Model ID is missing. Cannot call generateImage API.');
    generationError.value = 'Model ID is missing. Please select a model from the toolbar.';
    updateNodeData(props.id, { error: generationError.value, status: 'failed' });
    isGenerating.value = false;
    return; // Stop execution if no model is available
  }

  console.log('Generating image with prompt:', inputValue.value);
  isGenerating.value = true;
  generationError.value = null;
  uploadedImage.value = null; // Clear previous image
  // aspectRatio.value = '1/1'; // Resetting aspect ratio, or let the new image define it.
  // The API doesn't take aspectRatio directly, it's part of the prompt.

  // Update node data to save the prompt and clear image/result
  updateNodeData(props.id, {
    input: inputValue.value,
    imageUrl: null,
    // aspectRatio: null, // Will be updated after image generation based on actual image or if specified in prompt
    // style: 'Original', // Style is now part of the prompt
    backgroundRemoved: false,
    error: null, // 清除旧错误
    taskId: null, // 清除旧任务ID
    status: 'processing', // 设置初始状态为 processing
  });

  // Construct the prompt text, incorporating style and aspect ratio if they are not default
  let promptText = inputValue.value;
  const selectedRatio = props.data.aspectRatio;
  const selectedStyle = props.data.style;

  if (selectedRatio && RATIO_LABELS[selectedRatio] && selectedRatio !== '1/1') {
    // Assuming '1/1' is default if not specified
    promptText += `, aspect ratio ${RATIO_LABELS[selectedRatio]}`;
  }
  if (selectedStyle && selectedStyle !== 'Original') {
    // Assuming 'Original' is default
    promptText += `, style ${selectedStyle}`;
  }

  // Initialize content array with the text prompt
  const contentArray = [
    {
      type: 'text',
      text: promptText,
    },
  ];

  // Add connected image URLs to the content array
  if (connectedImageNodes.value && connectedImageNodes.value.length > 0) {
    connectedImageNodes.value.forEach((node) => {
      if (node.imageUrl) {
        contentArray.unshift({
          // Add image_url objects before the text prompt, or adjust order as needed
          type: 'image_url',
          image_url: {
            url: node.imageUrl,
          },
        });
      }
    });
  }

  const payload = {
    model: currentModel,
    messages: [
      {
        role: 'user',
        content: contentArray, // Use the dynamically constructed contentArray
      },
    ],
    stream: true,
  };

  try {
    // Call backend API for image generation
    const response = await backendApi.post('/generate-image', payload);

    const taskId = response.data.task_id; // Corrected to task_id based on API doc

    if (taskId) {
      updateNodeData(props.id, { taskId: taskId, status: 'processing' });
      pollTaskStatus(taskId);
    } else {
      throw new Error('Task ID not received from server.');
    }
  } catch (error) {
    console.error('Error starting image generation task:', error);
    generationError.value =
      error.response?.data?.error ||
      error.response?.data?.message ||
      'Failed to start image generation task. Please try again.';
    updateNodeData(props.id, { error: generationError.value, status: 'failed' });
    isGenerating.value = false; // 如果启动任务失败，则停止生成状态
  }
};

// 添加新的 pollTaskStatus 函数
const pollTaskStatus = (taskId) => {
  // 清除旧的轮询（如果存在）
  if (pollingIntervalId.value) {
    clearInterval(pollingIntervalId.value);
    pollingIntervalId.value = null;
  }

  pollingIntervalId.value = setInterval(async () => {
    try {
      // 实际的API路径已根据您的后端API文档进行调整
      const statusResponse = await backendApi.get(`/task/${taskId}`); // CHANGED from /task-status/
      const taskData = statusResponse.data;

      console.log(`Polling task ${taskId}, status:`, taskData.status);

      // 更新节点状态
      updateNodeData(props.id, { status: taskData.status });

      if (taskData.status === 'completed') {
        clearInterval(pollingIntervalId.value);
        pollingIntervalId.value = null;
        isGenerating.value = false;

        const generatedImageUrl = taskData.result; // CHANGED from taskData.imageUrl, as per API doc for GetTaskStatus

        if (!generatedImageUrl) {
          console.error('Task completed but no result URL found.');
          generationError.value = 'Image generation completed, but no image URL was returned.';
          updateNodeData(props.id, { error: generationError.value, imageUrl: null, status: 'failed' }); // Mark as failed if no URL
          return;
        }

        uploadedImage.value = generatedImageUrl;
        // Clear error from node data upon success
        updateNodeData(props.id, { imageUrl: generatedImageUrl, error: null, taskId: null, status: 'completed' }); // Also clear taskId and set status to completed

        const img = new Image();
        img.onload = () => {
          const originalRatio = img.width / img.height;
          const newAspectRatio = findClosestRatio(originalRatio);
          aspectRatio.value = newAspectRatio;
          updateNodeData(props.id, { aspectRatio: newAspectRatio });
          emit('updateNodeInternals', props.id);
        };
        img.onerror = () => {
          console.error('Error loading generated image for aspect ratio calculation.');
          generationError.value = 'Generated image could not be loaded.';
          // Update node data with the error, but keep the URL if it was received
          updateNodeData(props.id, { error: generationError.value, aspectRatio: aspectRatio.value || '1/1' });
          emit('updateNodeInternals', props.id);
        };
        img.src = generatedImageUrl;
      } else if (taskData.status === 'failed') {
        clearInterval(pollingIntervalId.value);
        pollingIntervalId.value = null;
        isGenerating.value = false;
        // Use taskData.error as per GetTaskStatus API doc
        generationError.value = taskData.error || 'Image generation failed.';
        updateNodeData(props.id, { error: generationError.value, imageUrl: null, status: 'failed', taskId: null }); // Also clear taskId
      }
      // If status is 'processing' or other intermediate states, do nothing, wait for next poll.
      // Consider adding a max poll attempts or timeout mechanism here if tasks can get stuck.
    } catch (error) {
      clearInterval(pollingIntervalId.value);
      pollingIntervalId.value = null;
      isGenerating.value = false;
      console.error('Error polling task status:', error);
      // Use error.response.data.error if available, as per API doc (e.g., for 404 Task not found)
      generationError.value = error.response?.data?.error || 'Failed to get task status.';
      updateNodeData(props.id, { error: generationError.value, imageUrl: null, status: 'failed', taskId: null }); // Also clear taskId
    }
  }, 10000); // 每10秒轮询一次
};

// Handle send button click
const handleSendClick = () => {
  // 添加对isEnhancing状态的检查
  if (isGenerating.value || isEnhancing.value) return;
  
  // Update node data with the prompt
  updateNodeData(props.id, { input: inputValue.value });

  // If no image, trigger image generation
  if (!uploadedImage.value) {
    generateImage();
  }
};

// Toolbar action handlers
const handleToolbarAction = (actionId) => {
  // 先关闭所有子菜单
  const closeAllMenus = () => {
    showStyleMenu.value = false;
    showRatioMenu.value = false;
    showModelMenu.value = false; // Close model menu as well
  };

  switch (actionId) {
    case 'style': {
      const styleMenuShouldOpen = !showStyleMenu.value;
      closeAllMenus();
      if (styleMenuShouldOpen) {
        showStyleMenu.value = true;
        // Add global click listener (only when menu is open)
        const clickHandler = (e) => {
          if (!e.target.closest('.style-dropdown') && !e.target.closest('.toolbar-button')) {
            closeAllMenus();
            document.removeEventListener('click', clickHandler);
          }
        };
        document.addEventListener('click', clickHandler);
      }
      // If styleMenuShouldOpen was false, menu was open and closeAllMenus() closed it.
      break;
    }

    case 'ratio': {
      const ratioMenuShouldOpen = !showRatioMenu.value;
      closeAllMenus();
      if (ratioMenuShouldOpen) {
        showRatioMenu.value = true;
        const clickHandler = (e) => {
          if (!e.target.closest('.ratio-dropdown') && !e.target.closest('.toolbar-button')) {
            closeAllMenus();
            document.removeEventListener('click', clickHandler);
          }
        };
        document.addEventListener('click', clickHandler);
      }
      // If ratioMenuShouldOpen was false, menu was open and closeAllMenus() closed it.
      break;
    }

    case 'model': {
      const modelMenuShouldOpen = !showModelMenu.value;
      closeAllMenus();
      if (modelMenuShouldOpen) {
        showModelMenu.value = true;
        const clickHandler = (e) => {
          // Ensure the click is not on any dropdown (all use 'style-dropdown' class or specific like 'ratio-dropdown')
          // or toolbar button
          if (!e.target.closest('.style-dropdown') && !e.target.closest('.toolbar-button')) {
            closeAllMenus();
            document.removeEventListener('click', clickHandler);
          }
        };
        document.addEventListener('click', clickHandler);
      }
      // If modelMenuShouldOpen was false, menu was open and closeAllMenus() closed it.
      break;
    }

    case 'bg-remove':
      handleCreateBgRemovedNode(); // 新函数调用
      closeAllMenus();
      break;

    case 'enhance':
      handleEnhancePrompt(); // 我们稍后会创建这个函数
      closeAllMenus();
      break;

    case 'describe':
      handleDescribeImage();
      closeAllMenus();
      break;

    default:
      closeAllMenus();
      break;
  }
};

// Apply style to the image
const applyStyle = (styleName) => {
  // 首先关闭菜单
  showStyleMenu.value = false;

  if (uploadedImage.value && styleName !== 'Original') {
    // 如果有上传的图片且选择的不是 'Original' 样式，则创建新节点
    const currentNode = findNode(props.id);
    if (!currentNode) {
      console.warn('Source node not found for applying style to new node.');
      return;
    }

    const newNodePosition = findNextAvailablePosition(currentNode, 'image', getNodes.value);
    const promptText = `Change to ${styleName} style`;

    const nodeDetails = {
      type: 'image',
      position: newNodePosition,
      data: {
        input: promptText,
        model: props.data?.model || (MODEL_OPTIONS.length > 0 ? MODEL_OPTIONS[0].id : undefined),
        aspectRatio: props.data?.aspectRatio || '1/1',
        imageUrl: null,
      },
    };
    const newNodeId = flowStore.addNodeWithDetails(nodeDetails);

    if (newNodeId) {
      const edgeDetails = {
        source: props.id,
        target: newNodeId,
        animated: true,
        label: `style: ${styleName}`,
        style: { stroke: '#4caf50' },
      };
      flowStore.addEdgeWithDetails(edgeDetails);
    }

    setTimeout(() => fitView());
    // 注意：在这种情况下，不再更新当前节点的样式
    // updateNodeData(props.id, { style: styleName });
  } else {
    // 如果没有上传图片，或者选择的是 'Original' 样式，则按原逻辑更新当前节点的样式
    updateNodeData(props.id, {
      style: styleName,
    });
  }
};

// Change aspect ratio
const changeAspectRatio = (ratio) => {
  // 无论是否上传图片都保存比例到节点数据
  updateNodeData(props.id, {
    aspectRatio: ratio,
  });

  // 仅当已上传图片时创建连接节点
  // 无论是否上传图片都保存比例到节点数据
  updateNodeData(props.id, {
    aspectRatio: ratio,
  });

  // 仅当已上传图片时创建连接节点
  if (uploadedImage.value) {
    const currentNode = findNode(props.id);
    if (!currentNode) {
      console.warn('Source node not found for changing aspect ratio.');
      return;
    }

    const newNodePosition = findNextAvailablePosition(currentNode, 'image', getNodes.value);
    const promptText = `Change image ratio to ${RATIO_LABELS[ratio]}`;

    const nodeDetails = {
      type: 'image',
      position: newNodePosition,
      data: {
        input: promptText,
        model: props.data?.model || (MODEL_OPTIONS.length > 0 ? MODEL_OPTIONS[0].id : undefined),
        aspectRatio: ratio,
        style: 'Original',
        imageUrl: null,
      },
    };
    const newNodeId = flowStore.addNodeWithDetails(nodeDetails);

    if (newNodeId) {
      const edgeDetails = {
        source: props.id,
        target: newNodeId,
        animated: true,
        label: 'change ratio',
        style: { stroke: '#646cff' },
      };
      flowStore.addEdgeWithDetails(edgeDetails);
    }

    setTimeout(() => fitView());
  }

  showRatioMenu.value = false;
};

// Select model
const selectedModelIcon = computed(() => {
  const currentModelId = props.data.model;
  if (currentModelId) {
    const model = MODEL_OPTIONS.find(m => m.id === currentModelId);
    if (model && model.icon) {
      return model.icon;
    }
  }
  // Fallback to first model's icon if available
  if (MODEL_OPTIONS.length > 0 && MODEL_OPTIONS[0].icon) {
    return MODEL_OPTIONS[0].icon;
  }
  // Final fallback to default icon
  return 'mdi:cube-outline';
});

const selectModel = (modelId) => {
  updateNodeData(props.id, { model: modelId });
  showModelMenu.value = false; // Close menu after selection
};

// 添加 onUnmounted 钩子以清除轮询
onUnmounted(() => {
  if (pollingIntervalId.value) {
    clearInterval(pollingIntervalId.value);
    pollingIntervalId.value = null;
  }
});

// 新增：处理创建背景移除节点的操作
const handleCreateBgRemovedNode = () => {
  if (!uploadedImage.value) return; // 确保有图片可供操作

  const currentNode = findNode(props.id);
  if (!currentNode) {
    console.warn('Source node not found for creating background removed node.');
    return;
  }

  const newNodePosition = findNextAvailablePosition(currentNode, 'image', getNodes.value);
  const promptText = 'Remove background';

  const nodeDetails = {
    type: 'image',
    position: newNodePosition,
    data: {
      input: promptText,
      style: props.data?.style || 'Original',
      model: props.data?.model || (MODEL_OPTIONS.length > 0 ? MODEL_OPTIONS[0].id : undefined),
      imageUrl: null,
      aspectRatio: props.data?.aspectRatio || '1/1',
    },
  };
  const newNodeId = flowStore.addNodeWithDetails(nodeDetails);

  if (newNodeId) {
    const edgeDetails = {
      source: props.id,
      target: newNodeId,
      animated: true,
      label: 'remove background',
      style: { stroke: '#ff69b4' },
    };
    flowStore.addEdgeWithDetails(edgeDetails);
  }

  setTimeout(() => fitView({ nodes: [props.id, newNodeId], animated: true, minZoom: 0.5, maxZoom: 1 })); // Fit view to both nodes
};

// 新增：处理 Enhance Prompt 功能
const handleEnhancePrompt = async () => {
  if (inputValue.value.trim() === '' || isEnhancing.value || isGenerating.value) {
    console.warn('Enhance Prompt: Prompt is empty or another operation is in progress.');
    if (inputValue.value.trim() === '') {
      enhanceError.value = 'Please enter a prompt to enhance.';
      // 清除错误信息，如果用户之后输入了内容
      watch(
        inputValue,
        (newVal) => {
          if (newVal.trim() !== '') enhanceError.value = null;
        },
        { once: true }
      );
    }
    return;
  }

  isEnhancing.value = true;
  enhanceError.value = null;
  generationError.value = null; // 同时清除图片生成错误

  const originalPrompt = inputValue.value; // 保存原始提示词以便出错时恢复
  const currentModel = props.data.model || (MODEL_OPTIONS.length > 0 ? MODEL_OPTIONS[0].id : 'sora_image');
  const payload = {
    prompt: originalPrompt,
    target_model: currentModel,
    stream: false,
  };

  const activeController = new AbortController();

  try {
    const response = await backendApi.post('/refine-prompt', payload, {
      signal: activeController.signal,
    });

    if (response.data && typeof response.data.answer === 'string') {
      const refinedPrompt = response.data.answer;
      inputValue.value = refinedPrompt;
      updateNodeData(props.id, { input: refinedPrompt });
      enhanceError.value = null;
    } else {
      console.error('Error: Invalid response structure from /refine-prompt', response.data);
      enhanceError.value = 'Failed to enhance prompt: Invalid response from server.';
      inputValue.value = originalPrompt;
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('Prompt enhancement request aborted.');
    } else if (error.response) {
      console.error('Error enhancing prompt (API):', error.response.data);
      enhanceError.value = error.response.data?.error || error.response.data?.message || 'Failed to enhance prompt.';
      inputValue.value = originalPrompt;
    } else {
      console.error('Error enhancing prompt (Network/Client):', error);
      enhanceError.value = error.message || 'An unexpected error occurred during prompt enhancement.';
      inputValue.value = originalPrompt;
    }
  } finally {
    isEnhancing.value = false;
  }
};

// Calculate left position for model menu
const calculateModelMenuLeft = () => {
  const buttonWidth = 40; // Width of toolbar buttons
  const gap = 4; // Gap between toolbar buttons
  const modelButtonIndex = toolbarActions.value.findIndex((action) => action.id === 'model');

  if (modelButtonIndex !== -1) {
    return `${modelButtonIndex * (buttonWidth + gap)}px`;
  }
  // Fallback if model button is not found, assumes it's the 3rd button (index 2)
  // Style (0), Ratio (1), Model (2) -> 2 * (40 + 4) = 88px
  return '88px';
};

// 添加处理右侧 handle 点击事件的函数
const showFullTextTooltip = (event, text) => {
  if (!text) return;

  const element = event.target;
  const isTruncated = element.scrollWidth > element.clientWidth;

  if (isTruncated) {
    // 清除现有的工具提示
    const tooltipContainer = document.getElementById('global-tooltip-container');
    while (tooltipContainer.firstChild) {
      tooltipContainer.removeChild(tooltipContainer.firstChild);
    }

    // 创建并显示工具提示
    const tooltip = document.createElement('div');
    tooltip.className = 'global-text-tooltip';
    tooltip.textContent = text;

    // 获取输入框元素的位置
    const rect = element.getBoundingClientRect();

    // 将位置放在输入框下方
    tooltip.style.position = 'fixed';
    tooltip.style.left = `${rect.left}px`;
    tooltip.style.top = `${rect.bottom + 5}px`; // 在输入框下方5像素处

    tooltipContainer.appendChild(tooltip);

    // 添加移除事件
    const removeTooltip = () => {
      if (tooltipContainer.contains(tooltip)) {
        tooltipContainer.removeChild(tooltip);
      }
      element.removeEventListener('mouseleave', removeTooltip);
    };

    element.addEventListener('mouseleave', removeTooltip);
  }
};

const handleRightHandleClick = (event) => {
  event.stopPropagation(); // 阻止事件冒泡，防止触发其他点击事件

  // 获取点击位置
  const clickPosition = {
    x: event.clientX,
    y: event.clientY,
  };

  // 调用 flowStore 显示上下文菜单，传递当前节点 ID
  flowStore.showHandleContextMenu(clickPosition, props.id);
};
</script>

<template>
  <!-- Wrap everything in a single root element to fix the fragment warning -->
  <div class="image-node-wrapper">
    <div class="node-label" :class="{ selected: data?.selected }">image {{ id.split('_')[1] }}</div>
    <!-- Main toolbar with nested submenus -->
    <NodeToolbar :is-visible="data?.selected" :position="toolbarPosition" class="image-toolbar">
      <button
        v-for="action in toolbarActions"
        :key="action.id"
        type="button"
        class="toolbar-button"
        :class="{
          selected:
            (action.id === 'style' && showStyleMenu) ||
            (action.id === 'ratio' && showRatioMenu) ||
            (action.id === 'model' && showModelMenu),
        }"
        :disabled="action.disabled"
        @click.stop="handleToolbarAction(action.id)">
        <Icon :name="action.id === 'model' ? selectedModelIcon.value : action.icon" />
        <span class="tooltip">{{ action.label }}</span>
      </button>

      <!-- Style submenu with thumbnails -->
      <div v-show="showStyleMenu" class="style-dropdown">
        <div class="style-options" @wheel.stop>
          <div class="style-grid">
            <button
              v-for="style in STYLE_OPTIONS"
              :key="style.name"
              :class="{
                'style-option': true,
                selected: data?.style === style.name,
              }"
              @click="applyStyle(style.name)">
              <Icon :name="style.icon" class="icon style-icon" />
              <span class="tooltip">{{ style.label }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Aspect ratio submenu -->
      <div v-show="showRatioMenu" class="style-dropdown ratio-dropdown" style="left: 0; top: 40px; z-index: 1001">
        <div class="style-options" @wheel.stop>
          <div class="style-grid">
            <button
              v-for="ratio in filteredRatios"
              :key="ratio"
              :class="{
                'ratio-option': true,
                selected: data?.aspectRatio === ratio,
              }"
              @click="changeAspectRatio(ratio)">
              {{ RATIO_LABELS[ratio] }}
            </button>
          </div>
        </div>
      </div>

      <!-- Model submenu -->
      <div
        v-show="showModelMenu"
        class="style-dropdown"
        :style="{ left: calculateModelMenuLeft(), top: '40px', zIndex: 1001, minWidth: '220px' }">
        <div class="style-options" @wheel.stop>
          <div class="model-options-layout">
            <!-- Custom layout class for model options -->
            <button
              v-for="modelItem in MODEL_OPTIONS"
              :key="modelItem.id"
              :class="{
                'ratio-option': true /* Reuse ratio-option for base styling */,
                'model-button-override': true /* For any specific model button styling */,
                selected: data?.model === modelItem.id,
              }"
              @click="selectModel(modelItem.id)">
              {{ modelItem.label }}
            </button>
          </div>
        </div>
      </div>
    </NodeToolbar>

    <div
      class="image-node vue-flow__node"
      :style="{
        backgroundImage: uploadedImage ? `url(${uploadedImage})` : '',
        aspectRatio: aspectRatio || 'auto',
      }"
      :class="[uploadedImage && data?.backgroundRemoved ? 'background-removed' : '']"
      @mousemove="(evt) => (toolbarPosition = { x: evt.clientX, y: evt.clientY - 40 })"
      @mouseenter="isHovering = true"
      @mouseleave="handleMouseLeave">
      <input ref="fileInput" type="file" accept="image/*" class="hidden-input" @change="handleFileUpload" />

      <template v-if="!uploadedImage">
        <!-- 优先显示连接的图片 -->
        <div v-if="!uploadedImage && connectedImageNodes.length > 0" class="connected-images">
          <div class="connected-images-title">Connected Images</div>
          <div class="connected-images-container">
            <div v-for="imageNode in connectedImageNodes" :key="imageNode.id" class="image-thumbnail-container">
              <div class="image-thumbnail" :style="{ backgroundImage: `url(${imageNode.imageUrl})` }">
                <div class="image-tooltip">
                  <span>From Image {{ imageNode.id.split('_')[1] }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 修改 action-section 的显示条件 -->
        <div v-else class="action-section">
          <div class="action-title">Try to...</div>
          <ul class="action-list">
            <li @click.stop="triggerFileInput">
              <Icon name="mdi:upload" class="icon" />
              <span>Upload an image</span>
            </li>
            <li>
              <Icon name="mdi:merge" class="icon" />
              <span>Combine images into a video</span>
            </li>
            <li>
              <Icon name="mdi:image" class="icon" />
              <span>Turn an image into a video</span>
            </li>
            <li>
              <Icon name="mdi:comment-question" class="icon" />
              <span>Ask a question about an image</span>
            </li>
          </ul>
        </div>
      </template>

      <div v-if="uploadedImage" class="replace-button" @click.stop="triggerFileInput">
        <Icon name="mdi:image-refresh" class="icon" />
        Replace Image
      </div>

      <div v-if="!uploadedImage" class="bottom-container">
        <div class="bottom-section">
          <div v-if="isInputActive" class="input-area">
            <input
              ref="inputRef"
              v-model="inputValue"
              type="text"
              placeholder="Enter text..."
              :disabled="isGenerating || isEnhancing"
              @blur="deactivateInputIfEmpty"
              @keydown.enter="handleSendClick"
              @mouseenter="showFullTextTooltip($event, inputValue)" />
            <div class="controls">
              <button class="send-button" :disabled="isGenerating || isEnhancing" @click="handleSendClick">
                <Icon v-if="!isGenerating && !isEnhancing" name="mdi:arrow-up" class="icon" />
                <Icon v-else name="mdi:loading" class="icon spinning" />
              </button>
            </div>
          </div>

          <div
            v-if="!isInputActive && !data?.input && !isGenerating"
            class="suggestion-text"
            @click="activateInput"
            @mouseenter="showFullTextTooltip($event, inputValue)">
            Try "A playful doodle of a dancing cat"
          </div>

          <div v-if="generationError" class="error-message">
            {{ generationError }}
          </div>
          <!-- New enhanceError display -->
          <div v-if="enhanceError" class="error-message" style="margin-top: 5px">
            {{ enhanceError }}
          </div>
        </div>
      </div>
      <Handle
        id="handle-left"
        type="target"
        :class="['handle', 'handle-left']"
        :position="Position.Left"
        :style="{ opacity: isHovering ? 1 : 0 }" />
      <Handle
        id="handle-right"
        type="source"
        :class="['handle', 'handle-right']"
        :position="Position.Right"
        :style="{ opacity: isHovering ? 1 : 0 }"
        @click="handleRightHandleClick" />
    </div>
  </div>
</template>

<style scoped>
.image-node-wrapper {
  position: relative;
}

.image-node {
  touch-action: none;
  cursor: grab;
  width: 300px;
  max-width: 600px;
  min-width: 200px;
  height: auto;
  background-color: #2d2d2d;
  position: relative;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 200px;
  border-radius: 12px;
  color: #e0e0e0;
  padding: 15px 0;
  font-family: sans-serif;
  border: 1px solid #444;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin: 0;
  display: flex;
  flex-direction: column;
  transition:
    box-shadow 0.2s ease,
    border-color 0.2s ease,
    transform 0.2s ease;
}

.image-node[style*='aspect-ratio']:has(.replace-button) {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  padding: 0;
}

.image-node:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
  background-color: #2d2d2d;
}

.node-label {
  position: absolute;
  left: 0px;
  top: -24px;
  text-transform: uppercase;
  background: #646cff;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
  transform: scale(0.9);
  transition:
    transform 0.2s ease,
    opacity 0.2s ease;
  z-index: 2;
  pointer-events: none;
}

.image-node:hover .node-label,
.node-label.selected {
  transform: scale(1);
  opacity: 1;
}

.image-node.vue-flow__node.selected {
  background-color: #2d2d2d;
  box-shadow: 0 0 0 2px #646cff;
  border-color: #646cff;
  transform: scale(1.02);
  z-index: 10;
  cursor: grabbing;
}

/* Image filters for different styles */
.filter-vintage {
  filter: sepia(0.5) contrast(1.2) brightness(0.9);
}

.filter-noir {
  filter: grayscale(1) contrast(1.5) brightness(0.9);
}

.filter-sketch {
  filter: grayscale(1) contrast(2) brightness(1.5) blur(0.5px);
}

.filter-watercolor {
  filter: brightness(1.1) contrast(0.9) saturate(1.2) blur(0.5px);
}

/* Background removal effect */
.background-removed {
  background-color: transparent !important;
  mix-blend-mode: multiply;
}

:deep(.vue-flow__node.selected) .vue-flow__handle {
  background: #646cff;
  border-color: #fff;
}

.replace-button {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition:
    opacity 0.2s ease,
    min-width 0.2s ease;
  max-width: 60%;
  width: auto;
  justify-content: center;
  background: rgba(40, 40, 40, 0.7);
  color: #fff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  border: 1px solid #646cff;
  z-index: 2;
  white-space: nowrap;
}

.image-node:hover .replace-button {
  opacity: 1;
}

.replace-button:hover {
  background: rgba(100, 108, 255, 0.9);
}

.replace-button .icon {
  font-size: 16px;
}

.image-node > *:not(.handle) {
  transition: opacity 0.2s ease;
}

.image-node:hover > *:not(.handle) {
  opacity: 1;
}

.hidden-input {
  display: none;
}

.action-section {
  margin: 0 20px 20px 20px;
}

.action-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #fff;
  text-align: left;
}

.action-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.action-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  font-size: 12px;
  border-bottom: 1px solid #444;
  cursor: pointer;
  transition: background 0.2s ease;
  text-align: left;
}

.action-list li > span {
  flex: 1;
}

.action-list li:hover {
  background: rgba(255, 255, 255, 0.05);
}

.action-list .icon {
  margin-right: 12px;
  font-size: 20px;
  color: #646cff;
}

.action-list li:last-child {
  border-bottom: none;
}

.suggestion-text {
  color: #7a7a7a;
  font-size: 14px;
  padding: 8px 0;
  cursor: text;
  transition: color 0.2s ease;
  text-align: left;
}

.suggestion-text:hover {
  color: #9a9a9a;
  background: rgba(255, 255, 255, 0.02);
}

/* Styles for the static input display */
.static-display {
  padding: 8px 12px;
  background: rgba(45, 45, 45, 0.3);
  border-radius: 6px;
  border: 1px dashed #555;
  margin-bottom: 8px;
  position: relative;
  text-align: left;
  color: #e0e0e0;
  font-size: 14px;
  word-break: break-word;
  pointer-events: none;
  padding-right: 100px;
}

/* 统一处理中的徽章样式 */
.processing-badge {
  background: rgba(138, 155, 255, 0.15) !important;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(138, 155, 255, 0.3);
}

/* 确保输入区域在生成期间保持可见 */
.input-area[disabled] {
  opacity: 0.8;
  cursor: not-allowed;
}

.input-area {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.input-area input {
  flex: 1;
  min-width: 0;
  background: transparent;
  border: none;
  border-bottom: 1px solid #666;
  padding: 8px 0;
  color: #e0e0e0;
  font-size: 14px;
  transition: border-color 0.2s ease;
  cursor: text;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.input-area input:focus {
  outline: none;
  border-color: #646cff;
}

.processing-badge {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8a9bff;
  background: rgba(45, 45, 45, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
}

.processing-badge .icon {
  font-size: 14px; /* Adjust as needed */
}

.spinning {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.suggestion-text {
  color: #7a7a7a;
  font-size: 14px;
  padding: 8px 0;
  cursor: text;
  transition: color 0.2s ease;
  text-align: left;
}

.suggestion-text:hover {
  color: #9a9a9a;
  background: rgba(255, 255, 255, 0.02);
}

.input-area {
  display: flex;
  align-items: center;
  gap: 4px;
}

.input-area input {
  flex: 1;
  background: transparent;
  border: none;
  border-bottom: 1px solid #666;
  padding: 8px 0;
  color: #e0e0e0;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.input-area input:focus {
  outline: none;
  border-color: #646cff;
}

.input-area input::placeholder {
  color: #aaa;
}

.input-area input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #aaa;
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.badge {
  background: #444;
  color: #aaa;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.send-button {
  background: transparent;
  border: none;
  color: #646cff;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.send-button:hover {
  background-color: rgba(100, 108, 255, 0.1);
}

.handle {
  width: 20px;
  height: 20px;
  background: #646cff;
  border: 2px solid #fff;
  border-radius: 50%;
  position: absolute;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition:
    opacity 0.2s ease,
    transform 0.2s ease;
  cursor: crosshair;
}

.handle::before,
.handle::after {
  content: '';
  position: absolute;
  background: #fff;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.handle::before {
  width: 12px;
  height: 2px;
}

.handle::after {
  width: 2px;
  height: 12px;
}

.handle-left {
  left: -10px !important;
  transform: translateY(-50%);
}

.handle-right {
  right: -10px !important;
  transform: translateY(-50%);
}

.handle:hover {
  background: #747bff;
  transform: scale(1.2) translateY(-50%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.vue-flow__node.selected) .handle {
  background: #ff7e67;
  border-color: #fff;
}

/* Optimized edge styles */
:deep(.vue-flow__edge) {
  stroke: #646cff; /* Default stroke color */
  stroke-width: 2px;
  transition:
    stroke 0.2s ease,
    stroke-width 0.2s ease; /* Smooth transition for hover/select */
}

:deep(.vue-flow__edge.selected) {
  stroke: #ff7e67;
  stroke-width: 3px;
}

.image-toolbar {
  background: rgba(45, 45, 45, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(68, 68, 68, 0.5);
  border-radius: 6px;
  padding: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  display: flex;
  gap: 4px;
  z-index: 1000;
}

.toolbar-button {
  position: relative;
  padding: 8px;
  border-radius: 4px;
  width: 40px;
  height: 40px;
  transition:
    background 0.2s ease,
    transform 0.2s ease;
  background: rgba(45, 45, 45, 0.9);
  backdrop-filter: blur(4px);
  border: 1px solid #666;
  color: #e0e0e0;
  cursor: pointer;
  z-index: 1002; /* Ensure buttons stay above menus */
}

.toolbar-button:hover {
  background: rgba(100, 108, 255, 0.25);
  border-color: rgba(100, 108, 255, 0.5);
  transform: scale(1.05);

  .icon {
    color: #747bff;
  }
}

.toolbar-button.selected {
  background: #646cff;
  border-color: #646cff;
  color: white;
}

.tooltip {
  opacity: 0;
  position: absolute;
  left: 50%;
  top: -30px;
  transform: translateX(-50%);
  white-space: nowrap;
  background: rgba(30, 30, 30, 0.4);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.95);
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1005;
  pointer-events: none;
  transition:
    opacity 0.2s ease,
    transform 0.2s ease;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.toolbar-button:hover .tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(-2px);
}

.tooltip::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -5px;
  transform: translateX(-50%);
  border-width: 5px 5px 0;
  border-style: solid;
  border-color: transparent transparent rgba(50, 50, 50, 0.4);
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}

.image-toolbar .icon {
  position: relative;
  z-index: 1;
  font-size: 18px;
}

/* Style dropdown menu - matching App.vue popup-menu */
.style-dropdown {
  background: rgba(45, 45, 45, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px 12px 12px; /* More top padding for tooltips */
  box-shadow:
    0 10px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 1px rgba(255, 255, 255, 0.05);
  z-index: 1001;
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 40px; /* Move dropdown further down */
  max-height: 70vh;
  /* overflow-y: auto; */
}

.ratio-dropdown {
  padding: 8px;
  min-width: 80px;
}

.ratio-dropdown .style-grid {
  grid-template-columns: repeat(2, minmax(60px, 1fr));
  gap: 6px;
}

.ratio-option {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border-radius: 8px;
  background: rgba(68, 68, 68, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.ratio-option:hover {
  background: rgba(100, 108, 255, 0.4);
  transform: none;
}

/* 添加禁用状态样式 */
.ratio-option.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

.ratio-option.selected {
  background: #646cff;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* 移除不再需要的图标样式 */
.ratio-dropdown .style-icon {
  display: none;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(32px, 1fr));
  gap: 12px;
  padding: 12px;
  overflow: visible;
  /* 添加这些属性 */
  position: relative;
  z-index: 1;
}

.style-option {
  position: relative;
  width: 32px;
  height: 32px;
  padding: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(68, 68, 68, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: visible;
  /* 添加这个属性，让hover时提升z-index */
  z-index: 1;
}

.style-option:hover {
  background: rgba(100, 108, 255, 0.4);
  border-color: rgba(100, 108, 255, 0.7);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  /* 添加这行，hover时提升元素层级 */
  z-index: 2;
}

.style-option.disabled {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.style-option.selected {
  background: rgba(100, 108, 255, 0.6);
  border-color: #646cff;
  box-shadow: 0 0 0 2px #646cff;
}

.style-icon {
  font-size: 16px;
  margin-bottom: 2px;
  transition: transform 0.2s ease;
}

.style-option:not(.selected) .icon {
  font-size: 16px;
  opacity: 0.9;
}

.style-option:hover .style-icon {
  transform: scale(1.1);
}

.style-option .tooltip {
  opacity: 0;
  position: absolute;
  left: 50%;
  bottom: calc(100% + 8px); /* Position above instead of below */
  transform: translateX(-50%);
  white-space: nowrap;
  background: rgba(30, 30, 30, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.95);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  transition:
    opacity 0.3s ease,
    transform 0.2s ease;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  z-index: 1010;
  min-width: max-content;
  text-align: center;
}

.style-option:hover .tooltip {
  opacity: 1;
}

.style-option .tooltip::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 100%; /* Arrow now points down */
  transform: translateX(-50%);
  border-width: 6px 6px 0; /* Triangle pointing down */
  border-style: solid;
  border-color: rgba(30, 30, 30, 0.9) transparent transparent;
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1));
}

/* Custom scrollbar matching App.vue */
.style-dropdown::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.style-dropdown::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.style-dropdown::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 3px;
}

.style-dropdown::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Style dropdown header */
.style-dropdown-header {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  color: #f0f0f0;
  border-bottom: 1px solid #404040;
  margin-bottom: 8px;
}

/* Connected images styles */
.connected-images {
  padding: 0 20px;
  margin-bottom: 5px;
  border-top: 1px solid #444;
  padding-top: 10px;
}

.connected-images-title {
  font-size: 12px;
  color: #aaa;
  margin-bottom: 8px;
  font-weight: 500;
}

.connected-images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-thumbnail-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  border: 1px solid #444;
}

.image-thumbnail-container:hover {
  transform: scale(1.05);
  border-color: #646cff;
  box-shadow: 0 3px 8px rgba(100, 108, 255, 0.3);
}

.image-thumbnail {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.image-tooltip {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  text-align: center;
}

.image-thumbnail-container:hover .image-tooltip {
  opacity: 1;
}

/* Enhance prompt button */
.toolbar-button[data-id='enhance'] {
  width: auto;
  padding: 8px 12px;
}

.toolbar-button[data-id='enhance'] .icon {
  margin-right: 6px;
}

.generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 5;
  font-size: 16px;
}

.model-options-layout {
  display: flex;
  flex-direction: column;
  gap: 6px; /* Space between model buttons */
  padding: 8px; /* Padding inside the dropdown */
}

/* Styling for individual model buttons, reusing .ratio-option as a base */
.model-options-layout .ratio-option.model-button-override {
  width: 100%; /* Make buttons take full width of the layout */
  text-align: left; /* Align text to the left for longer model names */
  justify-content: flex-start; /* Align content (icon + text) to the left */
  padding: 8px 12px; /* Adjust padding for better appearance */
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(45, 45, 45, 0.7); /* Optional: slightly different background for disabled */
  border-color: #555; /* Optional: darker border for disabled */
  color: #888; /* Optional: dimmed color for disabled */
}

.toolbar-button:disabled:hover {
  background: rgba(45, 45, 45, 0.7); /* Keep disabled background on hover */
  border-color: #555; /* Keep disabled border on hover */
  transform: none; /* No transform on hover for disabled buttons */
}

.toolbar-button:disabled .icon {
  color: #888; /* Optional: dimmed icon color for disabled */
}

/* Optional: Hide tooltip for disabled buttons */
.toolbar-button:disabled .tooltip {
  display: none;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.bottom-container {
  width: 100%;
  padding: 0 20px 15px;
  margin-top: auto;
}
</style>
